load("//adev/shared-docs:defaults.bzl", "ts_project", "zoneless_jasmine_test")
load("//adev/shared-docs/pipeline/api-gen/rendering:render_api_to_html.bzl", "render_api_to_html")

render_api_to_html(
    name = "test",
    srcs = [
        "fake-cli-entries.json",
        "fake-entries.json",
    ],
)

ts_project(
    name = "unit_test_lib",
    testonly = True,
    srcs = glob(
        [
            "**/*.spec.mts",
        ],
    ),
    deps = [
        "//adev:node_modules/@types/jsdom",
        "//adev:node_modules/jsdom",
        "//adev/shared-docs/pipeline/api-gen/rendering:render_api_to_html_lib",
    ],
)

zoneless_jasmine_test(
    name = "unit_tests",
    data = [
        ":unit_test_lib",
        "//adev:node_modules/jsdom",
    ] + glob([
        "**/*.json",
    ]),
)
