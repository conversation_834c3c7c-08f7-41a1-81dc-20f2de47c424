/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

import {bootstrapApplication} from '@angular/platform-browser';
import {appConfig} from './app/app.config.js';
import {App} from './app/app';

bootstrapApplication(App, appConfig).catch((err) => console.error(err));
