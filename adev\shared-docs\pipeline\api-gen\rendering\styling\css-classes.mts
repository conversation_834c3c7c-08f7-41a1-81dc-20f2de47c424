/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// TODO(jelbourn): all of these CSS classes should use the `docs-` prefix.

export const API_REFERENCE_CONTAINER = 'docs-api';

export const PARAM_KEYWORD_CLASS_NAME = 'docs-param-keyword';
export const PARAM_GROUP_CLASS_NAME = 'docs-param-group';

export const REFERENCE_MEMBERS = 'docs-reference-members';
export const REFERENCE_DEPRECATED = 'docs-reference-deprecated';
export const REFERENCE_MEMBER_CARD = 'docs-reference-member-card';
export const REFERENCE_MEMBER_CARD_HEADER = 'docs-reference-card-header';
export const REFERENCE_MEMBER_CARD_BODY = 'docs-reference-card-body';
export const REFERENCE_MEMBER_CARD_ITEM = 'docs-reference-card-item';

export const HEADER_CLASS_NAME = 'docs-reference-header';
export const HEADER_ENTRY_CATEGORY = 'docs-reference-category';
export const HEADER_ENTRY_TITLE = 'docs-reference-title';
export const HEADER_ENTRY_LABEL = 'docs-api-item-label';

export const SECTION_CONTAINER = 'docs-reference-section';
export const SECTION_HEADING = 'docs-reference-section-heading';
export const SECTION_SUB_HEADING = 'docs-reference-section-heading--sub';
