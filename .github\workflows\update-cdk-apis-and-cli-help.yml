name: Update ADEV Angular CDK APIs and CLI Help

on:
  workflow_dispatch:
    inputs: {}
  push:
    branches:
      - 'main'
      - '[0-9]+.[0-9]+.x'

# Declare default permissions as read only.
permissions:
  contents: read

jobs:
  update_adev_docs:
    name: Update Angular CDK APIs and CLI Help (if necessary)
    if: github.repository == 'angular/angular'
    runs-on: ubuntu-latest
    # Prevents multiple concurrent runs of this workflow for the same branch
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: true

    steps:
      - name: Checkout the repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # Setting `persist-credentials: false` prevents the github-action account from being the
          # account that is attempted to be used for authentication, instead the remote is set to
          # an authenticated URL.
          persist-credentials: false

      # Angular CDK
      - name: Generate CDK apis
        run: node adev/scripts/update-cdk-apis/index.mjs
        env:
          ANGULAR_CDK_BUILDS_READONLY_GITHUB_TOKEN: ${{ secrets.ANGULAR_CDK_BUILDS_READONLY_GITHUB_TOKEN }}
      - name: Create a PR CDK apis (if necessary)
        uses: peter-evans/create-pull-request@v7.0.8 # v7.0.8
        with:
          token: ${{ secrets.ANGULAR_ROBOT_ACCESS_TOKEN }}
          push-to-fork: 'angular-robot/angular'
          delete-branch: true
          maintainer-can-modify: false
          branch: docs-update-cdk-apis-${{github.ref_name}}
          committer: Angular Robot <<EMAIL>>
          author: Angular Robot <<EMAIL>>
          title: 'docs: update Angular CDK apis [${{github.ref_name}}]'
          body: |
            Updated Angular CDK api files.
          labels: |
            action: merge
            area: docs
          commit-message: |
            docs: update Angular CDK apis

            Updated Angular CDK api files.

      # Angular CLI
      - name: Reset to current GitHub ref
        run: |
          git reset --hard ${{ github.ref }}
      - name: Generate CLI help
        run: node adev/scripts/update-cli-help/index.mjs
        env:
          ANGULAR_CLI_BUILDS_READONLY_GITHUB_TOKEN: ${{ secrets.ANGULAR_CLI_BUILDS_READONLY_GITHUB_TOKEN }}
      - name: Create a PR CLI help (if necessary)
        uses: peter-evans/create-pull-request@v7.0.8 # v7.0.8
        with:
          token: ${{ secrets.ANGULAR_ROBOT_ACCESS_TOKEN }}
          push-to-fork: 'angular-robot/angular'
          delete-branch: true
          maintainer-can-modify: false
          branch: docs-update-cli-help-${{github.ref_name}}
          committer: Angular Robot <<EMAIL>>
          author: Angular Robot <<EMAIL>>
          title: 'docs: update Angular CLI help [${{github.ref_name}}]'
          body: |
            Updated Angular CLI help contents.
          labels: |
            action: merge
            area: docs
          commit-message: |
            docs: update Angular CLI help

            Updated Angular CLI help contents.
