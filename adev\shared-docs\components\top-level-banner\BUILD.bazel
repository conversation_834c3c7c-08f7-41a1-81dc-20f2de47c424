load("//adev/shared-docs:defaults.bzl", "ng_project", "sass_binary", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "top-level-banner",
    srcs = [
        "top-level-banner.component.ts",
    ],
    assets = [
        ":top-level-banner.component.css",
        "top-level-banner.component.html",
    ],
    visibility = [
        "//adev/shared-docs/components:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/components/icon",
        "//adev/shared-docs/directives",
        "//adev/shared-docs/providers",
    ],
)

sass_binary(
    name = "style",
    src = "top-level-banner.component.scss",
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["*.spec.ts"],
    ),
    deps = [
        ":top-level-banner",
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/providers",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
