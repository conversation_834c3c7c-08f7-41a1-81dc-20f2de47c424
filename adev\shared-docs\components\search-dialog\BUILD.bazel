load("//adev/shared-docs:defaults.bzl", "ng_project", "sass_binary", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "search-dialog",
    srcs = [
        "search-dialog.component.ts",
    ],
    assets = [
        ":search-dialog.component.css",
        "search-dialog.component.html",
    ],
    visibility = [
        "//adev/shared-docs/components:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/cdk",
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/forms",
        "//adev:node_modules/@angular/router",
        "//adev:node_modules/rxjs",
        "//adev/shared-docs/components/algolia-icon",
        "//adev/shared-docs/components/search-history",
        "//adev/shared-docs/components/text-field",
        "//adev/shared-docs/directives",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/pipes",
        "//adev/shared-docs/services",
    ],
)

sass_binary(
    name = "style",
    src = "search-dialog.component.scss",
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["*.spec.ts"],
    ),
    deps = [
        ":search-dialog",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/platform-browser",
        "//adev:node_modules/@angular/router",
        "//adev/shared-docs/components/algolia-icon",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/services",
        "//adev/shared-docs/testing",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
