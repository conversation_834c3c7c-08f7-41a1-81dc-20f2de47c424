load("//adev/shared-docs:defaults.bzl", "ng_project", "sass_binary")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "icon",
    srcs = [
        "icon.component.ts",
    ],
    assets = [
        ":icon.component.css",
    ],
    visibility = [
        "//adev/shared-docs/components:__pkg__",
        "//adev/shared-docs/components/copy-source-code-button:__pkg__",
        "//adev/shared-docs/components/navigation-list:__pkg__",
        "//adev/shared-docs/components/table-of-contents:__pkg__",
        "//adev/shared-docs/components/text-field:__pkg__",
        "//adev/shared-docs/components/top-level-banner:__pkg__",
        "//adev/shared-docs/components/viewers:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
    ],
)

sass_binary(
    name = "style",
    src = "icon.component.scss",
)
