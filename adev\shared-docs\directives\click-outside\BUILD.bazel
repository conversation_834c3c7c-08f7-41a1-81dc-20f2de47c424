load("//adev/shared-docs:defaults.bzl", "ng_project", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "click-outside",
    srcs = [
        "click-outside.directive.ts",
    ],
    visibility = [
        "//adev/shared-docs/directives:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
    ],
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["*.spec.ts"],
    ),
    deps = [
        ":click-outside",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/platform-browser",
        "//adev:node_modules/@angular/router",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
