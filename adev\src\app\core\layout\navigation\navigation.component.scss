@use '@angular/docs/styles/media-queries' as mq;

// primary nav
:host {
  display: flex;
  position: sticky;
  top: 0;
  z-index: var(--z-index-nav);

  .adev-mobile-nav-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  // render tablet primary nav under blur when secondary is open
  @include mq.for-tablet {
    &:has(.adev-nav-primary--open) {
      z-index: 50;
    }
  }

  @include mq.for-tablet-landscape-down {
    width: 100%;

    .wrapper {
      width: 100%;
    }
  }
}

.adev-mobile-nav-bar {
  display: none;
  gap: 0.75rem;
  backdrop-filter: blur(16px);
  background-color: color-mix(in srgb, var(--page-background) 70%, transparent);
  position: relative;
  width: 100%;
  padding-block: 0.75rem;
  padding-inline: var(--layout-padding);
  border-block-end: 1px solid var(--septenary-contrast);
  box-sizing: border-box;

  transform: translateY(0);
  // mobile bar: enter with delay
  transition: transform 0.3s ease-out 0.6s;

  @include mq.for-phone-only {
    display: flex;
  }

  &:has(+ .adev-nav-primary--open) {
    transform: translateY(-100%);
    // mobile bar: exit without delay
    transition: transform 0.3s ease-in;
  }

  docs-icon {
    color: var(--primary-contrast);
  }
}

// First level navigation
.adev-nav-primary {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-height: 100vh;
  overflow: auto;
  backdrop-filter: blur(16px);
  background-color: color-mix(in srgb, var(--page-background) 70%, transparent);
  // render primary nav / mini menu above tablet secondary bar
  z-index: 250;
  position: relative;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
  height: 100dvh;
  padding-block-start: 1rem;
  padding-block-end: 2rem;
  box-sizing: border-box;
  // Add a subtle border for the home page
  border-block-end: 1px solid var(--septenary-contrast);

  @include mq.for-tablet-landscape-up {
    border-inline-end: 1px solid var(--septenary-contrast);
  }

  @include mq.for-phone-only {
    border-inline-end: 1px solid var(--septenary-contrast);
  }

  @include mq.for-tablet {
    flex-direction: row;
    width: 100%;
    padding-inline: calc(var(--layout-padding) - 1.25rem);
    height: auto;
    padding-block: 0;
  }

  &.adev-nav-primary--next,
  &.adev-nav-primary--rc {
    // change nav background to indicate this is the rc docs
    background: linear-gradient(
      140deg,
      color-mix(in srgb, var(--orange-red), transparent 60%) 0%,
      color-mix(in srgb, var(--vivid-pink), transparent 40%) 15%,
      color-mix(in srgb, var(--electric-violet), transparent 70%) 25%,
      color-mix(in srgb, var(--bright-blue), transparent 60%) 90%
    );
  }

  &.adev-nav-primary--deprecated {
    // change nav background to indicate this is an outdated version of the docs
    background-color: var(--deprecated-docs-bg);
  }

  // div containing mobile close button and adev-nav_top
  > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  @include mq.for-phone-only {
    position: absolute;
    top: 0;
    background-color: var(--page-background);
    box-shadow: 10px 4px 3px 0 rgba(0, 0, 0, 0.001);

    transform: translateX(-100%);
    // primary nav: exit delayed
    // TODO: should only be delayed if there is a secondary nav
    transition: transform 0.3s ease-in 0.38s;

    &.adev-nav-primary--open {
      transform: translateX(0%);
      // primary nav: enter after mobile nav bar exits
      transition: transform 0.3s ease-out 0.1s;
    }

    @media (prefers-reduced-motion: reduce-motion) {
      transition: none;
    }
  }
}

.adev-nav {
  &__top {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;

    @include mq.for-tablet {
      flex-direction: row;
    }

    // version dropdown button
    .adev-version-button {
      border: 1px solid var(--senary-contrast);
      border-radius: 0.25rem;
      width: fit-content;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      gap: 0.25rem;
      color: var(--quaternary-contrast);
      fill: var(--quaternary-contrast);
      transition: color 0.3s ease;
      font-size: 0.8rem;
      font-weight: 500;

      &:hover {
        color: var(--primary-contrast);
      }

      docs-icon {
        font-size: inherit;
        line-height: inherit;
        transition: transform 0.2s ease;
      }

      @include mq.for-phone-only {
        &.adev-mini-menu-open {
          &::after {
            transform: rotate(-90deg);
          }
        }
      }
      @include mq.for-tablet-landscape-up {
        &.adev-mini-menu-open {
          &::after {
            transform: rotate(-90deg);
          }
        }
      }
    }

    @include mq.for-tablet {
      > li:first-of-type {
        padding-inline-start: 1.25rem;
      }

      li {
        padding-inline: 0.875rem;
      }
    }
  }

  &__bottom {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @include mq.for-tablet {
      flex-direction: row !important;
      margin-inline-end: 1.25rem;
      gap: 0.75rem;
    }

    .adev-nav-item--active {
      button {
        docs-icon {
          color: var(--primary-contrast);
        }
      }
    }

    button {
      border: none;
      background-color: transparent;
      cursor: pointer;
      width: 100%;
      padding-inline: 1rem;

      @include mq.for-tablet {
        padding-inline: 0.5rem;
      }

      docs-icon {
        color: var(--quaternary-contrast);
        font-size: 1.5rem;
        @include mq.for-tablet-landscape-down {
          font-size: 1.25rem;
        }
      }

      &:hover {
        docs-icon {
          color: var(--primary-contrast);
        }
      }
    }
  }
}

.adev-nav-item--logo {
  a {
    height: 34px;
  }

  @include mq.for-tablet {
    gap: 0.75rem;
  }
}

.adev-close-nav {
  display: none;
  color: var(--primary-contrast);

  @include mq.for-phone-only {
    display: block;
  }
}

.adev-search-desktop {
  height: 1.375rem;
  text-transform: capitalize;
  @include mq.for-tablet-landscape-down {
    display: none;
  }
}

.adev-sub-navigation-hidden {
  display: none;
}

.adev-secondary-tablet-bar {
  font-size: 0.875rem;
  backdrop-filter: blur(16px);
  background-color: color-mix(in srgb, var(--page-background) 70%, transparent);
  border-block-end: 1px solid var(--septenary-contrast);
  padding-block: 1rem;
  padding-inline: var(--layout-padding);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;

  button {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    color: var(--primary-contrast);
    padding: 0;
    font-weight: 500;
  }

  @include mq.for-tablet-landscape-up {
    display: none;
  }

  @include mq.for-phone-only {
    display: none;
  }
}
