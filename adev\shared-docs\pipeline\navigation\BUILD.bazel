load("//adev/shared-docs:defaults.bzl", "esbuild", "ts_project")

package(default_visibility = ["//visibility:public"])

ts_project(
    name = "lib",
    srcs = glob(
        [
            "*.mts",
        ],
        exclude = [
            "index.mts",
        ],
    ),
    deps = [
        "//adev:node_modules/@types/node",
        "//adev:node_modules/@webcontainer/api",
        "//adev:node_modules/tinyglobby",
        "//adev/shared-docs/interfaces",
    ],
)

ts_project(
    name = "navigation",
    srcs = [
        "index.mts",
    ],
    visibility = [
        "//adev/shared-docs:__subpackages__",
    ],
    deps = [
        ":lib",
        "//adev:node_modules/@types/node",
        "//adev/shared-docs/interfaces",
    ],
)

esbuild(
    name = "bundle",
    config = "//adev/shared-docs/pipeline:esbuild-config",
    entry_point = ":index.mts",
    format = "esm",
    output = "navigation.mjs",
    platform = "node",
    target = "es2022",
    tsconfig = "//adev/shared-docs:tsconfig_build",
    visibility = ["//visibility:public"],
    deps = [
        ":navigation",
    ],
)
