load("//adev/shared-docs:defaults.bzl", "ng_project", "ts_project")

package(default_visibility = ["//visibility:private"])

ts_project(
    name = "pipes",
    srcs = [
        "index.ts",
    ],
    visibility = ["//adev/shared-docs:__subpackages__"],
    deps = [
        ":lib",
    ],
)

ng_project(
    name = "lib",
    srcs = glob(
        [
            "**/*.ts",
        ],
        exclude = [
            "index.ts",
            "**/*.spec.ts",
        ],
    ),
    deps = [
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/utils",
    ],
)
