name: DevInfra

on:
  push:
  pull_request_target:
    types: [opened, synchronize, reopened, ready_for_review, labeled]

# Declare default permissions as read only.
permissions:
  contents: read

jobs:
  assistant_to_the_branch_manager:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false
      - uses: angular/dev-infra/github-actions/branch-manager@4d2f875ec29ee71e0fe1a349a99c5ab2ccb71e30
        with:
          angular-robot-key: ${{ secrets.ANGULAR_ROBOT_PRIVATE_KEY }}
