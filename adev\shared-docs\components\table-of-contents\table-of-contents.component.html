<aside>
  <nav>
    <header>
      <h2 class="docs-title">On this page</h2>
    </header>
    <ul class="docs-faceted-list">
      <!-- TODO: Hide li elements with class docs-toc-item-h3 for laptop, table and phone screen resolutions  -->
      @for (item of tableOfContentItems(); track item.id) {
      <li class="docs-faceted-list-item" [class.docs-toc-item-h2]="item.level === TableOfContentsLevel.H2"
        [class.docs-toc-item-h3]="item.level === TableOfContentsLevel.H3">
        <!-- Not using routerLink + fragment because of: https://github.com/angular/angular/issues/30139 -->
        <a [href]="location.path() + '#' + item.id" [class.docs-faceted-list-item-active]="item.id === activeItemId()">
          {{ item.title }}
        </a>
      </li>
      }
    </ul>
  </nav>
  <button type="button" (click)="scrollToTop()">
    <docs-icon role="presentation">arrow_upward_alt</docs-icon>
    Back to the top
  </button>
</aside>