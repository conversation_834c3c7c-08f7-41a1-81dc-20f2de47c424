name: OpenSSF Scorecard
on:
  branch_protection_rule:
  schedule:
    - cron: '0 0 * * 0'
  push:
    branches: [main]
  workflow_dispatch:

# Declare default permissions as read only.
permissions:
  contents: read

jobs:
  analysis:
    name: Scorecards analysis
    runs-on: ubuntu-latest
    permissions:
      # Needed to upload the results to code-scanning dashboard.
      security-events: write
      # Needed to publish results
      id-token: write
      actions: read
      contents: read

    steps:
      - name: 'Checkout code'
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: 'Run analysis'
        uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde # v2.4.2
        with:
          results_file: results.sarif
          results_format: sarif
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          publish_results: true

      # Upload the results as artifacts.
      - name: 'Upload artifact'
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: SARIF file
          path: results.sarif
          retention-days: 5

      # Upload the results to GitHub's code scanning dashboard.
      - name: 'Upload to code-scanning'
        uses: github/codeql-action/upload-sarif@51f77329afa6477de8c49fc9c7046c15b9a4e79d # v3.29.5
        with:
          sarif_file: results.sarif
