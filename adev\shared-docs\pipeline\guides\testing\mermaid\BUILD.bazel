load("//adev/shared-docs:defaults.bzl", "ts_project", "zoneless_jasmine_test")

ts_project(
    name = "unit_test_lib",
    testonly = True,
    srcs = glob([
        "*.spec.mts",
    ]),
    deps = [
        "//adev:node_modules/@types/jsdom",
        "//adev:node_modules/jsdom",
        "//adev:node_modules/marked",
        "//adev/shared-docs/pipeline/guides:guides_lib",
    ],
)

zoneless_jasmine_test(
    name = "unit_tests",
    data = [
        ":unit_test_lib",
        "@rules_browsers//src/browsers/chromium",
    ] + glob([
        "**/*.md",
        "**/*.svg",
        "**/*.mts",
    ]),
    env = {
        "CHROME_HEADLESS_BIN": "$(CHROME-HEADLESS-SHELL)",
    },
    toolchains = ["@rules_browsers//src/browsers/chromium:toolchain_alias"],
)
