load("//adev/shared-docs:defaults.bzl", "ts_project")

package(default_visibility = ["//visibility:private"])

ts_project(
    name = "components",
    srcs = [
        "index.ts",
    ],
    visibility = [
        "//adev/shared-docs:__pkg__",
    ],
    deps = [
        "//adev/shared-docs/components/algolia-icon",
        "//adev/shared-docs/components/breadcrumb",
        "//adev/shared-docs/components/cookie-popup",
        "//adev/shared-docs/components/copy-source-code-button",
        "//adev/shared-docs/components/icon",
        "//adev/shared-docs/components/navigation-list",
        "//adev/shared-docs/components/search-dialog",
        "//adev/shared-docs/components/select",
        "//adev/shared-docs/components/slide-toggle",
        "//adev/shared-docs/components/table-of-contents",
        "//adev/shared-docs/components/text-field",
        "//adev/shared-docs/components/top-level-banner",
        "//adev/shared-docs/components/viewers",
    ],
)
