load("//adev/shared-docs:defaults.bzl", "esbuild", "ts_project")

package(default_visibility = ["//visibility:public"])

ts_project(
    name = "stackblitz",
    srcs = glob(
        [
            "*.mts",
        ],
        exclude = [
            "index.mts",
        ],
    ),
    deps = [
        "//adev:node_modules/@types/jsdom",
        "//adev:node_modules/@types/node",
        "//adev:node_modules/jsdom",
        "//adev:node_modules/tinyglobby",
        "//adev/shared-docs/pipeline/examples/shared",
        "//adev/shared-docs/pipeline/guides:guides_lib",
    ],
)

ts_project(
    name = "index",
    srcs = [
        "index.mts",
    ],
    visibility = [
        "//adev/shared-docs:__subpackages__",
    ],
    deps = [
        ":stackblitz",
        "//adev:node_modules/@types/node",
    ],
)

esbuild(
    name = "bundle",
    config = "//adev/shared-docs/pipeline:esbuild-config",
    entry_point = ":index.mts",
    # JSDOM should not be bundled because it has workers and dynamic imports.
    external = ["jsdom"],
    format = "esm",
    output = "stackblitz.mjs",
    platform = "node",
    target = "es2022",
    tsconfig = "//adev/shared-docs:tsconfig_build",
    visibility = ["//visibility:public"],
    deps = [
        ":index",
    ],
)
