{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"analytics": false}, "newProjectRoot": "projects", "projects": {"example-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/example-app", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/assets"], "styles": ["src/styles.css"], "stylePreprocessorOptions": {"includePaths": ["node_modules/"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"browserTarget": "example-app:build:production"}, "development": {"browserTarget": "example-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"browserTarget": "example-app:build"}}, "test": {"builder": "@angular/build:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "css", "assets": ["src/assets"], "stylePreprocessorOptions": {"includePaths": ["node_modules/"]}, "styles": ["src/styles.css"], "scripts": []}}}}}}