<!-- The `messages.fr.xlf` after translation for documentation purposes -->
<!-- #docregion -->
<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template">
    <body>
      <!-- #docregion translated-hello-before -->
      <trans-unit id="introductionHeader" datatype="html">
        <source>Hello i18n!</source>
        <note priority="1" from="description">An introduction header for this sample</note>
        <note priority="1" from="meaning">User welcome</note>
      </trans-unit>
      <!-- #enddocregion translated-hello-before -->
      <!-- #docregion translated-hello -->
      <!-- #docregion custom-id -->
      <trans-unit id="introductionHeader" datatype="html">
      <!-- #enddocregion custom-id -->
        <source>Hello i18n!</source>
        <target>Bonjour i18n !</target>
        <note priority="1" from="description">An introduction header for this sample</note>
        <note priority="1" from="meaning">User welcome</note>
      </trans-unit>
      <!-- #enddocregion translated-hello -->
      <!-- #docregion translated-other-nodes -->
      <!-- #docregion generated-id -->
      <trans-unit id="ba0cc104d3d69bf669f97b8d96a4c5d8d9559aa3" datatype="html">
      <!-- #enddocregion generated-id -->
        <source>I don&apos;t output any element</source>
        <target>Je n'affiche aucun élément</target>
      </trans-unit>
      <trans-unit id="701174153757adf13e7c24a248c8a873ac9f5193" datatype="html">
        <source>Angular logo</source>
        <target>Logo d'Angular</target>
      </trans-unit>
      <!-- #enddocregion translated-other-nodes -->
      <!-- #docregion translated-plural -->
      <trans-unit id="5a134dee893586d02bffc9611056b9cadf9abfad" datatype="html">
        <source>{VAR_PLURAL, plural, =0 {just now} =1 {one minute ago} other {<x id="INTERPOLATION" equiv-text="{{minutes}}"/> minutes ago} }</source>
        <target>{VAR_PLURAL, plural, =0 {à l'instant} =1 {il y a une minute} other {il y a <x id="INTERPOLATION" equiv-text="{{minutes}}"/> minutes} }</target>
      </trans-unit>
      <!-- #enddocregion translated-plural -->
      <!-- #docregion translated-select -->
      <!-- #docregion translate-select-1 -->
      <trans-unit id="f99f34ac9bd4606345071bd813858dec29f3b7d1" datatype="html">
        <source>The author is <x id="ICU" equiv-text="{gender, select, male {...} female {...} other {...}}"/></source>
        <target>L'auteur est <x id="ICU" equiv-text="{gender, select, male {...} female {...} other {...}}"/></target>
      </trans-unit>
      <!-- #enddocregion translate-select-1 -->
      <!-- #docregion translate-select-2 -->
      <trans-unit id="eff74b75ab7364b6fa888f1cbfae901aaaf02295" datatype="html">
        <source>{VAR_SELECT, select, male {male} female {female} other {other} }</source>
        <target>{VAR_SELECT, select, male {un homme} female {une femme} other {autre} }</target>
      </trans-unit>
      <!-- #enddocregion translate-select-2 -->
      <!-- #enddocregion translated-select -->
      <!-- #docregion translate-nested -->
      <!-- #docregion translate-nested-1 -->
      <trans-unit id="972cb0cf3e442f7b1c00d7dab168ac08d6bdf20c" datatype="html">
        <source>Updated: <x id="ICU" equiv-text="{minutes, plural, =0 {...} =1 {...} other {...}}"/></source>
        <target>Mis à jour: <x id="ICU" equiv-text="{minutes, plural, =0 {...} =1 {...} other {...}}"/></target>
      </trans-unit>
      <!-- #enddocregion translate-nested-1 -->
      <!-- #docregion translate-nested-2 -->
      <trans-unit id="7151c2e67748b726f0864fc443861d45df21d706" datatype="html">
        <source>{VAR_PLURAL, plural, =0 {just now} =1 {one minute ago} other {<x id="INTERPOLATION" equiv-text="{{minutes}}"/> minutes ago by {VAR_SELECT, select, male {male} female {female} other {other} }} }</source>
        <target>{VAR_PLURAL, plural, =0 {à l'instant} =1 {il y a une minute} other {il y a <x id="INTERPOLATION" equiv-text="{{minutes}}"/> minutes par {VAR_SELECT, select, male {un homme} female {une femme} other {autre} }} }</target>
      </trans-unit>
      <!-- #enddocregion translate-nested-2 -->
      <!-- #enddocregion translate-nested -->
      <!-- #docregion i18n-duplicate-custom-id -->
      <trans-unit id="myId" datatype="html">
        <source>Hello</source>
        <target state="new">Bonjour</target>
      </trans-unit>
      <!-- #enddocregion i18n-duplicate-custom-id -->
    </body>
  </file>
</xliff>