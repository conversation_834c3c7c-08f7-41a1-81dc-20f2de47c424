/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

let highlighter: any;

export async function initHighlighter() {
  const {createHighlighter} = await import('shiki');
  highlighter = await createHighlighter({
    themes: ['github-light', 'github-dark'],
    langs: [
      'javascript',
      'typescript',
      'angular-html',
      'angular-ts',
      'shell',
      'html',
      'http',
      'json',
      'jsonc',
      'nginx',
      'markdown',
      'apache',
    ],
  });
}

export function codeToHtml(code: string, language: string | undefined): string {
  const html = highlighter.codeToHtml(code, {
    lang: language ?? 'text',
    themes: {
      light: 'github-light',
      dark: 'github-dark',
    },
    cssVariablePrefix: '--shiki-',
    defaultColor: false,
  });

  return html;
}

/**
 * This function is used to post-process the HTML generated by Shiki
 */
export function replaceKeywordFromShikiHtml(
  keyword: string,
  shikiHtml: string,
  replaceWith: string,
): string {
  return (
    shikiHtml
      // remove the leading space of the element after the keyword
      .replace(
        new RegExp(`(<[^>]*>(?:${keyword})<\\/\\w+><[^>]*>)(\\s)(\\w+<\\/\\w+>)`, 'g'),
        '$1$3',
      )
      // Shiki requires the keywords (eg. function,interface) for highlighting signatures
      // here we are replacing it
      .replace(new RegExp(`<[^>]*>(?:${keyword})<\\/\\w+>`, 'g'), replaceWith)
  );
}

export function insertParenthesesForDecoratorInShikiHtml(shikiHtml: string): string {
  // TODO: Look if we can use JSDom instead of regex

  return shikiHtml.replace(
    // This will match a structure like this:
    // @<span>decoratorName</span><span> {</span><span>...</span><span> }</span>
    // and insert a parenthese around the curly braces
    // @<span>decoratorName</span><span> ({</span><span>...</span><span> })</span>
    /(@\s*<[^>]*>.*?<\/\w+>\s*<[^>]*>)(\s*\{)(<\/\w+>.*<[^>]*>)(\s*\})(<\/\w+>)/gms,
    '$1({$3})$5',
  );
}
