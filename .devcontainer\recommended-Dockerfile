# Image metadata and config.
# Ideally, the Node.js version should match what we use on CI.
FROM cimg/node:18.13.0@sha256:45d0ee279f522c4562a464df71108104cc9b2187205333ffd0707d4f05589731


LABEL name="Angular dev environment" \
      description="This image can be used to create a dev environment for building Angular." \
      vendor="angular" \
      version="1.0"

EXPOSE 4000 4200 4433 5000 8080 9876


# Switch to `root`.
USER root


# Configure `Node.js`/`npm`.
RUN npm config --global set user root


# Go! (And keep going.)
CMD ["tail", "--follow", "/dev/null"]
