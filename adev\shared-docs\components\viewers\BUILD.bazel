load("//adev/shared-docs:defaults.bzl", "ng_project", "sass_binary", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "viewers",
    srcs = [
        "docs-viewer/docs-viewer.component.ts",
        "example-viewer/example-viewer.component.ts",
    ],
    assets = [
        ":docs-viewer/docs-viewer.component.css",
        ":example-viewer/example-viewer.component.css",
        "example-viewer/example-viewer.component.html",
    ],
    visibility = [
        "//adev/shared-docs/components:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/cdk",
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/material",
        "//adev:node_modules/@angular/router",
        "//adev:node_modules/rxjs",
        "//adev/shared-docs/components/breadcrumb",
        "//adev/shared-docs/components/copy-source-code-button",
        "//adev/shared-docs/components/table-of-contents",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/providers",
    ],
)

sass_binary(
    name = "example-viewer-style",
    src = "example-viewer/example-viewer.component.scss",
)

sass_binary(
    name = "docs-viewer-style",
    src = "docs-viewer/docs-viewer.component.scss",
    deps = [
        "//adev/shared-docs/styles",
    ],
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["**/*.spec.ts"],
    ),
    deps = [
        ":viewers",
        "//adev:node_modules/@angular/cdk",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/material",
        "//adev:node_modules/@angular/platform-browser",
        "//adev:node_modules/@angular/router",
        "//adev/shared-docs/components/breadcrumb",
        "//adev/shared-docs/components/copy-source-code-button",
        "//adev/shared-docs/components/icon",
        "//adev/shared-docs/components/table-of-contents",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/services",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
