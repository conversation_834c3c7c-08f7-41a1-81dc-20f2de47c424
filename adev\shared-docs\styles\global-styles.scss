// TODO: Continue organizing and refactoring this file
@use '@angular/material' as mat;
@use '@angular/cdk';

// Using disable-next-line to avoid stylelint errors - these imports are necessary
// TODO: Is there another way to prevent these linting errors?
// stylelint-disable-next-line @angular/no-unused-import
@use '_colors';
// stylelint-disable-next-line @angular/no-unused-import
@use '_z-index';

// Global
@use 'resets';
@use 'typography';
@use 'scroll-track';
@use 'button';
@use 'kbd';
@use 'api-item-label';
@use 'faceted-list';
@use 'media-queries' as mq;

// Docs
@use 'docs/alert';
@use 'docs/callout';
@use 'docs/card';
@use 'docs/code';
@use 'docs/decorative-header';
@use 'docs/icon';
@use 'docs/pill';
@use 'docs/steps';
@use 'docs/table';
@use 'docs/video';
@use 'docs/mermaid';

// Global
@include resets.resets();
@include typography.typography();
@include scroll-track.scroll-track();
@include button.button();
@include kbd.kbd();
@include api-item-label.api-item-label();
@include faceted-list.faceted-list();

@include mq.for-phone-only();
@include mq.for-tablet-portrait-up();
@include mq.for-tablet-landscape-up();
@include mq.for-desktop-up();
@include mq.for-extra-large-desktop-up();
@include mq.for-tablet-landscape-down();

// temporary just to show different options of code component UI.
$primary: mat.m2-define-palette(mat.$m2-indigo-palette);
$accent: mat.m2-define-palette(mat.$m2-pink-palette, A200, A100, A400);
$theme: mat.m2-define-light-theme(
  (
    color: (
      primary: $primary,
      accent: $accent,
    ),
    typography: mat.m2-define-typography-config(),
  )
);

// Include material core styles.
@include cdk.a11y-visually-hidden();
@include mat.tabs-theme($theme);
@include mat.button-toggle-theme($theme);
@include mat.tooltip-theme($theme);

// Include custom docs styles
@include alert.docs-alert();
@include callout.docs-callout();
@include card.docs-card();
@include code.docs-code-block();
@include code.docs-code-editor();
@include decorative-header.docs-decorative-header();
@include icon.docs-icon();
@include pill.docs-pill();
@include steps.docs-steps();
@include code.docs-syntax-highlighting();
@include table.docs-table();
@include video.docs-video();

// Include custom angular.dev styles

// Disable view transitions when reduced motion is requested.
@media (prefers-reduced-motion) {
  ::view-transition-group(*),
  ::view-transition-old(*),
  ::view-transition-new(*) {
    animation: none !important;
  }
}

.docs-dark-mode .shiki {
  color: var(--shiki-dark);
  background-color: var(--shiki-dark-bg);

  span {
    color: var(--shiki-dark);
    background-color: var(--shiki-dark-bg);
    /* Optional, if you also want font styles */
    font-style: var(--shiki-dark-font-style);
    font-weight: var(--shiki-dark-font-weight);
  }

  .shiki-ln-line-highlighted,
  button:hover {
    span {
      background-color: inherit;
    }
  }
}

.shiki {
  &.cli {
    padding-inline-start: 1rem;
    padding-block: 1rem;
  }

  a {
    color: inherit;

    &:hover {
      text-decoration: underline;
    }
  }
}

.docs-light-mode .shiki {
  color: var(--shiki-light);
  background-color: var(--shiki-light-bg);

  span {
    color: var(--shiki-light);
    background-color: var(--shiki-light-bg);
    /* Optional, if you also want font styles */
    font-style: var(--shiki-light-font-style);
    font-weight: var(--shiki-light-font-weight);
    text-decoration: var(--shiki-light-text-decoration);
  }

  .shiki-ln-line-highlighted,
  button:hover {
    span {
      background-color: inherit;
    }
  }
}
