load("//adev/shared-docs:defaults.bzl", "ng_project", "sass_binary", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ng_project(
    name = "cookie-popup",
    srcs = [
        "cookie-popup.component.ts",
    ],
    assets = [
        ":cookie-popup.component.css",
        "cookie-popup.component.html",
    ],
    visibility = [
        "//adev/shared-docs/components:__pkg__",
    ],
    deps = [
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/utils",
    ],
)

sass_binary(
    name = "style",
    src = "cookie-popup.component.scss",
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["*.spec.ts"],
    ),
    deps = [
        ":cookie-popup",
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/testing",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
