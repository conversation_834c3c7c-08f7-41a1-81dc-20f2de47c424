{"name": "angular.dev", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/platform-browser": "^20.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "typescript": "~5.8.2"}}