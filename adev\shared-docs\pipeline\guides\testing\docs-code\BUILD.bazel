load("//adev/shared-docs:defaults.bzl", "ts_project", "zoneless_jasmine_test")

ts_project(
    name = "docs-code",
    testonly = True,
    srcs = glob([
        "**/*.spec.mts",
    ]),
    deps = [
        "//adev:node_modules/@types/jsdom",
        "//adev:node_modules/jsdom",
        "//adev/shared-docs/pipeline/guides:guides_lib",
    ],
)

zoneless_jasmine_test(
    name = "test",
    data = [
        "docs-code.md",
        "example-with-eslint-comment.ts",
        "example-with-region.ts",
        "messages.fr.xlf.html",
        "new-code.ts",
        "old-code.ts",
        ":docs-code",
    ],
)
