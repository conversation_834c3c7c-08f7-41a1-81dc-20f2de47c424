load("//adev/shared-docs:defaults.bzl", "ts_project")

package(default_visibility = ["//visibility:private"])

ts_project(
    name = "interfaces",
    srcs = [
        "index.ts",
    ],
    visibility = ["//adev/shared-docs:__subpackages__"],
    deps = [
        ":lib",
    ],
)

ts_project(
    name = "lib",
    srcs = glob(
        [
            "**/*.ts",
        ],
        exclude = [
            "index.ts",
            "**/*.spec.ts",
        ],
    ),
    deps = [
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/router",
        "//adev:node_modules/@types/node",
        "//adev:node_modules/@webcontainer/api",
    ],
)
