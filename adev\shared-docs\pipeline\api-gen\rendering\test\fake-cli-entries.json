{"name": "generate", "command": "ng generate <schematic>", "shortDescription": "Generates and/or modifies files based on a schematic.", "aliases": ["g"], "deprecated": false, "options": [{"name": "defaults", "type": "boolean", "default": false, "description": "Disable interactive input prompts for options with a default."}, {"name": "dry-run", "type": "boolean", "aliases": ["d"], "default": false, "description": "Run through and reports activity without writing out results."}, {"name": "force", "type": "boolean", "default": false, "description": "Force overwriting of existing files."}, {"name": "help", "type": "boolean", "description": "Shows a help message for this command in the console."}, {"name": "interactive", "type": "boolean", "default": true, "description": "Enable interactive input prompts."}, {"name": "schematic", "type": "string", "description": "The [collection:schematic] to run.", "positional": 0}], "subcommands": [{"name": "app-shell", "command": "app-shell", "shortDescription": "Generates an application shell for running a server-side version of an app.", "options": [{"name": "project", "type": "string", "description": "The name of the related client app."}], "aliases": [], "deprecated": false}, {"name": "application", "command": "application [name]", "shortDescription": "Generates a new basic application definition in the \"projects\" subfolder of the workspace.", "options": [{"name": "zoneless", "type": "boolean", "default": false, "description": "Create an application that does not utilize zone.js."}, {"name": "inline-style", "type": "boolean", "aliases": ["s"], "description": "Include styles inline in the root component.ts file. Only CSS styles can be included inline. Default is false, meaning that an external styles file is created and referenced in the root component.ts file."}, {"name": "inline-template", "type": "boolean", "aliases": ["t"], "description": "Include template inline in the root component.ts file. Default is false, meaning that an external template file is created and referenced in the root component.ts file. "}, {"name": "minimal", "type": "boolean", "default": false, "description": "Create a bare-bones project without any testing frameworks. (Use for learning purposes only.)"}, {"name": "name", "type": "string", "description": "The name of the new application.", "positional": 0}, {"name": "prefix", "type": "string", "aliases": ["p"], "default": "app", "description": "A prefix to apply to generated selectors."}, {"name": "project-root", "type": "string", "description": "The root directory of the new application."}, {"name": "routing", "type": "boolean", "default": true, "description": "Creates an application with routing enabled."}, {"name": "skip-install", "type": "boolean", "default": false, "description": "Skip installing dependency packages."}, {"name": "skip-package-json", "type": "boolean", "default": false, "description": "Do not add dependencies to the \"package.json\" file."}, {"name": "skip-tests", "type": "boolean", "aliases": ["S"], "default": false, "description": "Do not create \"spec.ts\" test files for the application."}, {"name": "ssr", "type": "boolean", "default": false, "description": "Creates an application with Server-Side Rendering (SSR) and Static Site Generation (SSG/Prerendering) enabled."}, {"name": "standalone", "type": "boolean", "default": true, "description": "Creates an application based upon the standalone API, without NgModules."}, {"name": "strict", "type": "boolean", "default": true, "description": "Creates an application with stricter bundle budgets settings."}, {"name": "style", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less"], "description": "The file extension or preprocessor to use for style files."}, {"name": "view-encapsulation", "type": "string", "enum": ["Emulated", "None", "ShadowDom", "IsolatedShadowDom"], "description": "The view encapsulation strategy to use in the new application."}], "aliases": ["app"], "deprecated": false}, {"name": "class", "command": "class [name]", "shortDescription": "Creates a new, generic class definition in the given project.", "options": [{"name": "name", "type": "string", "description": "The name of the new class.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new class."}, {"name": "type", "type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}], "aliases": ["cl"], "deprecated": false}, {"name": "component", "command": "component [name]", "shortDescription": "Creates a new, generic component definition in the given project.", "options": [{"name": "change-detection", "type": "string", "aliases": ["c"], "default": "<PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "description": "The change detection strategy to use in the new component."}, {"name": "display-block", "type": "boolean", "aliases": ["b"], "default": false, "description": "Specifies if the style will contain `:host { display: block; }`."}, {"name": "export", "type": "boolean", "default": false, "description": "The declaring NgModule exports this component."}, {"name": "export-default", "type": "boolean", "default": false, "description": "Use default export for the component instead of a named export."}, {"name": "flat", "type": "boolean", "default": false, "description": "Create the new files at the top level of the current project."}, {"name": "inline-style", "type": "boolean", "aliases": ["s"], "default": false, "description": "Include styles inline in the component.ts file. Only CSS styles can be included inline. By default, an external styles file is created and referenced in the component.ts file."}, {"name": "inline-template", "type": "boolean", "aliases": ["t"], "default": false, "description": "Include template inline in the component.ts file. By default, an external template file is created and referenced in the component.ts file."}, {"name": "module", "type": "string", "aliases": ["m"], "description": "The declaring NgModule."}, {"name": "name", "type": "string", "description": "The name of the component.", "positional": 0}, {"name": "prefix", "type": "string", "aliases": ["p"], "description": "The prefix to apply to the generated component selector."}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "selector", "type": "string", "description": "The HTML selector to use for this component."}, {"name": "skip-import", "type": "boolean", "default": false, "description": "Do not import this component into the owning NgModule."}, {"name": "skip-selector", "type": "boolean", "default": false, "description": "Specifies if the component should have a selector or not."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new component."}, {"name": "standalone", "type": "boolean", "default": true, "description": "Whether the generated component is standalone."}, {"name": "style", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less", "none"], "description": "The file extension or preprocessor to use for style files, or 'none' to skip generating the style file."}, {"name": "type", "type": "string", "default": "Component", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}, {"name": "view-encapsulation", "type": "string", "aliases": ["v"], "enum": ["Emulated", "None", "ShadowDom", "IsolatedShadowDom"], "description": "The view encapsulation strategy to use in the new component."}], "aliases": ["c"], "deprecated": false}, {"name": "config", "command": "config [type]", "shortDescription": "Generates a configuration file in the given project.", "options": [{"name": "project", "type": "string", "description": "The name of the project."}, {"name": "type", "type": "string", "enum": ["karma", "browserslist"], "description": "Specifies which type of configuration file to create.", "positional": 0}], "aliases": [], "deprecated": false}, {"name": "directive", "command": "directive [name]", "shortDescription": "Creates a new, generic directive definition in the given project.", "options": [{"name": "export", "type": "boolean", "default": false, "description": "The declaring NgModule exports this directive."}, {"name": "flat", "type": "boolean", "default": true, "description": "When true (the default), creates the new files at the top level of the current project."}, {"name": "module", "type": "string", "aliases": ["m"], "description": "The declaring NgModule."}, {"name": "name", "type": "string", "description": "The name of the new directive.", "positional": 0}, {"name": "prefix", "type": "string", "aliases": ["p"], "description": "A prefix to apply to generated selectors."}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "selector", "type": "string", "description": "The HTML selector to use for this directive."}, {"name": "skip-import", "type": "boolean", "default": false, "description": "Do not import this directive into the owning NgModule."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new class."}, {"name": "standalone", "type": "boolean", "default": true, "description": "Whether the generated directive is standalone."}], "aliases": ["d"], "deprecated": false}, {"name": "enum", "command": "enum [name]", "shortDescription": "Generates a new, generic enum definition in the given project.", "options": [{"name": "name", "type": "string", "description": "The name of the enum.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project in which to create the enum. Default is the configured default project for the workspace."}, {"name": "type", "type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}], "aliases": ["e"], "deprecated": false}, {"name": "environments", "command": "environments", "shortDescription": "Generates and configures environment files for a project.", "options": [{"name": "project", "type": "string", "description": "The name of the project."}], "aliases": [], "deprecated": false}, {"name": "guard", "command": "guard [name]", "shortDescription": "Generates a new, generic route guard definition in the given project.", "options": [{"name": "flat", "type": "boolean", "default": true, "description": "When true (the default), creates the new files at the top level of the current project."}, {"name": "functional", "type": "boolean", "default": true, "description": "Specifies whether to generate a guard as a function."}, {"name": "implements", "type": "array", "aliases": ["guardType"], "description": "Specifies which type of guard to create."}, {"name": "name", "type": "string", "description": "The name of the new route guard.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new guard."}], "aliases": ["g"], "deprecated": false}, {"name": "interceptor", "command": "interceptor [name]", "shortDescription": "Creates a new, generic interceptor definition in the given project.", "options": [{"name": "flat", "type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, {"name": "functional", "type": "boolean", "default": true, "description": "Creates the interceptor as a `HttpInterceptorFn`."}, {"name": "name", "type": "string", "description": "The name of the interceptor.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new interceptor."}], "aliases": [], "deprecated": false}, {"name": "interface", "command": "interface [name] [type]", "shortDescription": "Creates a new, generic interface definition in the given project.", "options": [{"name": "name", "type": "string", "description": "The name of the interface.", "positional": 0}, {"name": "prefix", "type": "string", "description": "A prefix to apply to generated selectors."}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "type", "type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\".", "positional": 1}], "aliases": ["i"], "deprecated": false}, {"name": "library", "command": "library [name]", "shortDescription": "Creates a new, generic library project in the current workspace.", "options": [{"name": "entry-file", "type": "string", "default": "public-api", "description": "The path at which to create the library's public API file, relative to the workspace root."}, {"name": "name", "type": "string", "description": "The name of the library.", "positional": 0}, {"name": "prefix", "type": "string", "aliases": ["p"], "default": "lib", "description": "A prefix to apply to generated selectors."}, {"name": "project-root", "type": "string", "description": "The root directory of the new library."}, {"name": "skip-install", "type": "boolean", "default": false, "description": "Do not install dependency packages."}, {"name": "skip-package-json", "type": "boolean", "default": false, "description": "Do not add dependencies to the \"package.json\" file. "}, {"name": "skip-ts-config", "type": "boolean", "default": false, "description": "Do not update \"tsconfig.json\" to add a path mapping for the new library. The path mapping is needed to use the library in an app, but can be disabled here to simplify development."}, {"name": "standalone", "type": "boolean", "default": true, "description": "Creates a library based upon the standalone API, without NgModules."}], "aliases": ["lib"], "deprecated": false}, {"name": "module", "command": "module [name]", "shortDescription": "Creates a new, generic NgModule definition in the given project.", "options": [{"name": "flat", "type": "boolean", "default": false, "description": "Create the new files at the top level of the current project root. "}, {"name": "module", "type": "string", "aliases": ["m"], "description": "The declaring NgModule."}, {"name": "name", "type": "string", "description": "The name of the NgModule.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "route", "type": "string", "description": "The route path for a lazy-loaded module. When supplied, creates a component in the new module, and adds the route to that component in the `Routes` array declared in the module provided in the `--module` option."}, {"name": "routing", "type": "boolean", "default": false, "description": "Create a routing module."}, {"name": "routing-scope", "type": "string", "default": "Child", "enum": ["Child", "Root"], "description": "The scope for the new routing module."}], "aliases": ["m"], "deprecated": false}, {"name": "pipe", "command": "pipe [name]", "shortDescription": "Creates a new, generic pipe definition in the given project.", "options": [{"name": "export", "type": "boolean", "default": false, "description": "The declaring NgModule exports this pipe."}, {"name": "flat", "type": "boolean", "default": true, "description": "When true (the default) creates files at the top level of the project."}, {"name": "module", "type": "string", "aliases": ["m"], "description": "The declaring NgModule."}, {"name": "name", "type": "string", "description": "The name of the pipe.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-import", "type": "boolean", "default": false, "description": "Do not import this pipe into the owning NgModule."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new pipe."}, {"name": "standalone", "type": "boolean", "default": true, "description": "Whether the generated pipe is standalone."}], "aliases": ["p"], "deprecated": false}, {"name": "resolver", "command": "resolver [name]", "shortDescription": "Generates a new, generic resolver definition in the given project.", "options": [{"name": "flat", "type": "boolean", "default": true, "description": "When true (the default), creates the new files at the top level of the current project."}, {"name": "functional", "type": "boolean", "default": true, "description": "Creates the resolver as a `ResolveFn`."}, {"name": "name", "type": "string", "description": "The name of the new resolver.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new resolver."}], "aliases": ["r"], "deprecated": false}, {"name": "service", "command": "service [name]", "shortDescription": "Creates a new, generic service definition in the given project.", "options": [{"name": "flat", "type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, {"name": "name", "type": "string", "description": "The name of the service.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "skip-tests", "type": "boolean", "default": false, "description": "Do not create \"spec.ts\" test files for the new service."}], "aliases": ["s"], "deprecated": false}, {"name": "service-worker", "command": "service-worker", "shortDescription": "Pass this schematic to the \"run\" command to create a service worker", "options": [{"name": "project", "type": "string", "description": "The name of the project."}, {"name": "target", "type": "string", "default": "build", "description": "The target to apply service worker to."}], "aliases": [], "deprecated": false}, {"name": "web-worker", "command": "web-worker [name]", "shortDescription": "Creates a new, generic web worker definition in the given project.", "options": [{"name": "name", "type": "string", "description": "The name of the worker.", "positional": 0}, {"name": "project", "type": "string", "description": "The name of the project."}, {"name": "snippet", "type": "boolean", "default": true, "description": "Add a worker creation snippet in a sibling file of the same name."}], "aliases": [], "deprecated": false}]}