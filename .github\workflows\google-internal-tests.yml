name: Google Internal Tests Enforcement

on:
  pull_request_target:
    types: [opened, reopened, synchronize]

permissions: {}

jobs:
  trigger:
    permissions:
      pull-requests: read
      statuses: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: angular/dev-infra/github-actions/google-internal-tests@4d2f875ec29ee71e0fe1a349a99c5ab2ccb71e30
        with:
          run-tests-guide-url: http://go/angular-g3sync-start
          github-token: ${{ secrets.GITHUB_TOKEN }}
          sync-config: ./.ng-dev/google-sync-config.json
