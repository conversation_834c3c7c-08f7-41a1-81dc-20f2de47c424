<dialog #searchDialog>
  <div class="docs-search-container" (docsClickOutside)="closeSearchDialog()">
    <docs-text-field
      [autofocus]="true"
      [hideIcon]="true"
      [formControl]="searchControl"
      [resetLabel]="'Clear the search'"
      class="docs-search-input"
      placeholder="Search docs"
    />

    @if (searchResults().length) {
      <ul class="docs-search-results docs-mini-scroll-track">
        @for (result of searchResults(); track result.id) {
          <li docsSearchItem [item]="result">
            <a
              [routerLink]="'/' + result.url | relativeLink: 'pathname'"
              [fragment]="result.url | relativeLink: 'hash'"
              (click)="history.addItem(result)"
            >
              <div>
                <div class="docs-result-icon-and-type">
                  <!-- Icon -->
                  <span class="docs-search-result-icon" aria-hidden="true">
                    <i role="presentation" class="material-symbols-outlined docs-icon-small">
                      {{ result.type === 'code' ? 'code' : 'description'}}
                    </i>
                  </span>
                  <!-- Results type -->
                  <span class="docs-search-results__type" [innerHtml]="result.labelHtml"></span>
                </div>

                @if (result.subLabelHtml) {
                  <span
                    class="docs-search-results__type docs-search-results__lvl2"
                    [innerHtml]="result.subLabelHtml"
                  >
                  </span>
                }
              </div>

              <!-- Page title -->
              <span class="docs-result-page-title">{{ result.category }}</span>
            </a>
          </li>
        }
      </ul>
    } @else if (history.hasItems() && !this.searchControl.value.length) {
      <docs-search-history />
    } @else {
      <div class="docs-search-results docs-mini-scroll-track">
        @if (!resultsResource.hasValue()) {
          <div class="docs-search-results__start-typing">
            <span>Start typing to see results</span>
          </div>
        } @else if (searchResults().length === 0) {
          <div class="docs-search-results__no-results">
            <span>No results found</span>
          </div>
        }
      </div>
    }

    <div class="docs-search-footer">
      <ul class="docs-search-commands">
        <li>
          <kbd class="docs-search-commands-key">
            <svg width="15" height="15" aria-label="Enter key" role="img">
              <g
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.2"
              >
                <path d="M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"></path>
              </g>
            </svg>
          </kbd>
          <span>to select</span>
        </li>
        <li>
          <kbd class="docs-search-commands-key">
            <svg width="15" height="15" aria-label="Arrow down" role="img">
              <g
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.2"
              >
                <path d="M7.5 3.5v8M10.5 8.5l-3 3-3-3"></path>
              </g>
            </svg>
          </kbd>
          <kbd class="docs-search-commands-key">
            <svg width="15" height="15" aria-label="Arrow up" role="img">
              <g
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.2"
              >
                <path d="M7.5 11.5v-8M10.5 6.5l-3-3-3 3"></path>
              </g>
            </svg>
          </kbd>
          <span>to navigate</span>
        </li>
        <li>
          <kbd class="docs-search-commands-key"
            ><svg width="15" height="15" aria-label="Escape key" role="img">
              <g
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.2"
              >
                <path
                  d="M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"
                ></path>
              </g>
            </svg>
          </kbd>
          <span>to close</span>
        </li>
      </ul>
      <div class="docs-algolia">
        <span>Search by</span>
        <a
          target="_blank"
          rel="noopener"
          href="https://www.algolia.com/developers/?utm_source=angular.dev&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"
        >
          <docs-algolia-icon />
        </a>
      </div>
    </div>
  </div>
</dialog>
