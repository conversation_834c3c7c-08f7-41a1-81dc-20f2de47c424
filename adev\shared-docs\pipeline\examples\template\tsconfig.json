/* To learn more about this file see: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "es2022",
    "module": "es2020",
    "lib": ["es2020", "dom"],
    // Strictness flags. Matching the settings applied in the Angular Components source
    // code, ensuring that examples do not break in StackBlitz with stricter settings.
    "noUnusedParameters": false,
    "noUnusedLocals": false,
    "useDefineForClassFields": false,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "strictFunctionTypes": true,
    "noImplicitOverride": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictBindCallApply": true,
    "esModuleInterop": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
