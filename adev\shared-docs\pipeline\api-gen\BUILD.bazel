load("@npm2//:defs.bzl", "npm_link_all_packages")

package(default_visibility = ["//visibility:public"])

npm_link_all_packages()

# Expose the sources in the dev-infra NPM package.
filegroup(
    name = "files",
    srcs = glob(["*"]) + [
        "//adev/shared-docs/pipeline/api-gen/extraction:files",
        "//adev/shared-docs/pipeline/api-gen/manifest:files",
        "//adev/shared-docs/pipeline/api-gen/rendering:files",
    ],
)
