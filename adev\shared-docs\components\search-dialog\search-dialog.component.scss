dialog {
  background-color: transparent;
  border: none;
  padding-block-end: 3rem;
  margin: 0 auto;
  top: 15vh;

  &::backdrop {
    backdrop-filter: blur(5px);
  }
}

.docs-search-container {
  width: 750px;
  max-width: 90vw;
  background-color: var(--page-background);
  border: 1px solid var(--senary-contrast);
  border-radius: 0.25rem;
  box-sizing: border-box;

  .docs-search-input {
    border-radius: 0.25rem 0.25rem 0 0;
    border: none;
    border-block-end: 1px solid var(--senary-contrast);
    height: 2.6875rem; // 43px;
    padding-inline-start: 1rem;
    position: relative;
  }

  .docs-search-results {
    max-height: 50vh;
    overflow-y: auto;
    overscroll-behavior: contain;
    list-style-type: none;
    padding-inline: 0;
    padding-block-start: 1rem;
    padding-block-end: 1rem;
    margin: 0;
    border-block-end: 1px solid var(--senary-contrast);

    li {
      border-inline-start: 2px solid var(--senary-contrast);
      margin-inline-start: 1rem;
      padding-inline-end: 1rem;
      padding-block: 0.25rem;

      /**
      * This rule needs ng-deep to be applied to elements that are created via a [innerHTML] binding
      */
      ::ng-deep {
        mark {
          background: #e62600;
          background: var(--red-to-orange-horizontal-gradient);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
      }

      a {
        color: var(--secondary-contrast);
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;

        .docs-search-result-icon {
          i {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
          }
        }
      }

      &.active {
        background-color: var(--septenary-contrast); // stylelint-disable-line
      }

      &:hover,
      &.active {
        background-color: var(--octonary-contrast); // stylelint-disable-line
        border-inline-start: 2px solid var(--primary-contrast);
        a {
          span:not(.docs-result-page-title),
          .docs-search-results__type {
            color: var(--primary-contrast);
            i {
              color: var(--primary-contrast);
            }
          }
        }
      }
    }

    .docs-search-result-icon,
    .docs-search-results__type,
    .docs-result-page-title {
      color: var(--quaternary-contrast);
      display: inline-block;
      font-size: 0.875rem;
      transition: color 0.3s ease;
      padding: 0.75rem;
      padding-inline-end: 0;
    }

    .docs-search-results__lvl2 {
      display: inline-block;
      margin-inline-start: 2rem;
      padding-block-start: 0;
    }

    .docs-search-results__lvl3 {
      margin-inline-start: 2rem;
      padding-block-start: 0;
    }
  }

  .docs-result-page-title {
    font-size: 0.875rem;
    font-weight: 400;
  }
}

.docs-search-results__start-typing,
.docs-search-results__no-results {
  padding: 0.75rem;
  color: var(--gray-400);
}

.docs-result-icon-and-type {
  display: flex;

  .docs-search-results__type {
    padding-inline-start: 0;
  }
}

.docs-search-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--gray-400);
  padding: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--page-background);
  border-radius: 0 0 0.25rem 0.25rem;
  container-type: inline-size;
  container-name: footer;

  .docs-search-commands {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
    padding: 0;

    li {
      display: flex;
      gap: 0.25rem;
    }

    .docs-search-commands-key {
      display: flex;
      align-items: center;
    }
  }

  @container footer (max-width: 600px) {
    .docs-search-commands li {
      display: none;
    }
  }

  .docs-algolia {
    display: flex;
    gap: 0.25rem;
    align-items: center;
  }

  docs-algolia-icon {
    display: block;
    margin-block-start: 5px;
    margin-inline-start: 0.15rem;
    width: 4rem;
  }
}
