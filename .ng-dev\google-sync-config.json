{"syncedFilePatterns": ["LICENSE", "modules/benchmarks/**", "packages/**"], "separateFilePatterns": ["packages/core/primitives/**"], "alwaysExternalFilePatterns": ["packages/*", "packages/bazel/**", "packages/compiler-cli/linker/**", "packages/compiler-cli/src/ngtsc/sourcemaps/**", "packages/compiler-cli/src/ngtsc/testing/**", "packages/compiler-cli/private/bazel.ts", "packages/compiler-cli/private/localize.ts", "packages/compiler-cli/private/tooling.ts", "packages/compiler-cli/private/babel.d.ts", "packages/compiler-cli/src/bin/**", "packages/core/schematics/utils/tsurge/helpers/angular_devkit/**", "packages/docs/**", "packages/elements/schematics/**", "packages/examples/**", "packages/localize/**", "packages/private/**", "packages/service-worker/**", "packages/ssr/BUILD.bazel", "packages/common/locales/generate-locales-tool/**", "packages/common/locales/index.bzl", "packages/http/**", "**/.g<PERSON><PERSON><PERSON>", "**/.git<PERSON><PERSON>", "**/third_party/**", "**/tsconfig-build.json", "**/tsconfig-tsec.json", "**/tsconfig.json", "**/rollup.config.js", "**/BUILD.bazel", "**/*.md", "packages/**/integrationtest/**", "packages/**/test/**", "packages/zone.js/*", "packages/zone.js/dist/**", "packages/zone.js/doc/**", "packages/zone.js/example/**", "packages/zone.js/scripts/**"]}