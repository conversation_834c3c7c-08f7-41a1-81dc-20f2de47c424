load("//adev/shared-docs:defaults.bzl", "esbuild", "ts_project")

package(default_visibility = ["//visibility:public"])

ts_project(
    name = "editor",
    srcs = glob(
        [
            "*.mts",
        ],
        exclude = [
            "playground_index.mts",
            "tutorial_index.mts",
        ],
    ),
    deps = [
        "//adev:node_modules/@types/node",
        "//adev:node_modules/@webcontainer/api",
        "//adev:node_modules/tinyglobby",
        "//adev/shared-docs/interfaces",
    ],
)

ts_project(
    name = "playground",
    srcs = [
        "playground_index.mts",
    ],
    visibility = [
        "//adev/shared-docs:__subpackages__",
    ],
    deps = [
        ":editor",
        "//adev:node_modules/@types/node",
        "//adev/shared-docs/interfaces",
    ],
)

ts_project(
    name = "tutorial",
    srcs = [
        "tutorial_index.mts",
    ],
    visibility = [
        "//adev/shared-docs:__subpackages__",
    ],
    deps = [
        ":editor",
        "//adev:node_modules/@types/node",
        "//adev/shared-docs/interfaces",
    ],
)

esbuild(
    name = "playground_bundle",
    config = "//adev/shared-docs/pipeline:esbuild-config",
    entry_point = ":playground_index.mts",
    format = "esm",
    output = "playground.mjs",
    platform = "node",
    target = "es2022",
    tsconfig = "//adev/shared-docs:tsconfig_build",
    visibility = ["//visibility:public"],
    deps = [
        ":playground",
    ],
)

esbuild(
    name = "tutorial_bundle",
    config = "//adev/shared-docs/pipeline:esbuild-config",
    entry_point = ":tutorial_index.mts",
    format = "esm",
    output = "tutorial.mjs",
    platform = "node",
    target = "es2022",
    tsconfig = "//adev/shared-docs:tsconfig_build",
    visibility = ["//visibility:public"],
    deps = [
        ":tutorial",
    ],
)
