load("//adev/shared-docs:defaults.bzl", "ng_project", "ts_project", "zoneless_web_test_suite")

package(default_visibility = ["//visibility:private"])

ts_project(
    name = "services",
    srcs = [
        "index.ts",
    ],
    visibility = ["//adev/shared-docs:__subpackages__"],
    deps = [
        ":lib",
    ],
)

ng_project(
    name = "lib",
    srcs = glob(
        [
            "**/*.ts",
        ],
        exclude = [
            "index.ts",
            "**/*.spec.ts",
        ],
    ),
    deps = [
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev:node_modules/@angular/router",
        "//adev:node_modules/algoliasearch",
        "//adev:node_modules/rxjs",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/utils",
    ],
)

ts_project(
    name = "test_lib",
    testonly = True,
    srcs = glob(
        ["*.spec.ts"],
    ),
    deps = [
        ":lib",
        "//adev:node_modules/@angular/common",
        "//adev:node_modules/@angular/core",
        "//adev/shared-docs/interfaces",
        "//adev/shared-docs/providers",
        "//adev/shared-docs/testing",
    ],
)

zoneless_web_test_suite(
    name = "test",
    deps = [":test_lib"],
)
