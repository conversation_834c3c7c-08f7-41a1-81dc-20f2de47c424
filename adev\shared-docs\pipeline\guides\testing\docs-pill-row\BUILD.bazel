load("//adev/shared-docs:defaults.bzl", "ts_project", "zoneless_jasmine_test")

ts_project(
    name = "docs-pill-row",
    testonly = True,
    srcs = glob([
        "**/*.spec.mts",
    ]),
    deps = [
        "//adev:node_modules/@types/jsdom",
        "//adev:node_modules/jsdom",
        "//adev/shared-docs/pipeline/guides:guides_lib",
    ],
)

zoneless_jasmine_test(
    name = "test",
    data = [
        "docs-pill-row.md",
        ":docs-pill-row",
    ],
)
