{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"first-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"inlineTemplate": true, "inlineStyle": true, "style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/first-app", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "first-app:build:production"}, "development": {"buildTarget": "first-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "first-app:build"}}}}}}